package com.pulse.patient_safety.entrance.web.query.assembler;

import com.pulse.patient_safety.entrance.web.vo.PatientAllergyBaseVo;
import com.vs.code.AutoGenerated;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** PatientAllergyBaseVo数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "7530c7d3-849f-3153-a3b0-c945e5e35540")
public class PatientAllergyBaseVoDataAssembler {

    /** 组装PatientAllergyBaseVo数据 */
    @AutoGenerated(locked = true, uuid = "780cdf3f-b1c7-3299-bbea-b3456ff50510")
    public void assembleData(Map<String, PatientAllergyBaseVo> voMap) {
        if (MapUtils.isEmpty(voMap)) {
            return;
        }
        assembleDataCustomized(new ArrayList<>(voMap.values())); // 自定义处理逻辑
    }

    /** 批量自定义组装PatientAllergyBaseVo数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "99901618-d143-3ee9-affe-71736d12596b")
    public void assembleDataCustomized(List<PatientAllergyBaseVo> dataList) {
        // 自定义数据组装

    }
}
