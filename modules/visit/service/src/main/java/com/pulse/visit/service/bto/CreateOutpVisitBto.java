package com.pulse.visit.service.bto;

import com.pulse.visit.common.enums.VisitStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;

/**
 * <b>[源自]</b> OutpVisit
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "370f00ac-938a-47b1-aab9-2f07fcc52951|BTO|DEFINITION")
public class CreateOutpVisitBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 费用分类 */
    @AutoGenerated(locked = true, uuid = "f24b3c4d-d074-4738-9114-341804dde9c0")
    private String costCategories;

    /** 费用性质 */
    @AutoGenerated(locked = true, uuid = "97ce27fd-3fcc-427c-a538-06074f59a4cd")
    private String costNature;

    /** 初诊标识 */
    @AutoGenerated(locked = true, uuid = "1e80c933-87d0-4c3f-abdf-c6e442efa899")
    private Boolean firstVisitFlag;

    /** gcp项目ID */
    @AutoGenerated(locked = true, uuid = "f5db847d-2902-422e-b6d4-4470550eea2e")
    private String gcpProjectId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "43a59d8c-248a-4599-a6d5-745ac0838be0")
    private String id;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "b4569b20-4d91-41ab-b0a5-a6a08046de28")
    private String identityType;

    /** 挂号ID */
    @AutoGenerated(locked = true, uuid = "d4b12daa-cce4-4c19-b8ab-495ad57b721c")
    private String outpRegisterId;

    /** 病人去向 */
    @AutoGenerated(locked = true, uuid = "1dd846cb-d7c5-4275-8dd4-aa09f515bc11")
    private String patientDestination;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "39c03ba2-ed7e-455b-a633-08edc21f6b5a")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "37c4a108-2f75-4060-a6b2-6dff25f18765")
    private String patientName;

    /** 分诊序号 */
    @AutoGenerated(locked = true, uuid = "f5453ccb-3139-4c9e-ac7a-d3afb5cb0a1e")
    private Long triageSerialNumber;

    /** 分诊状态 */
    @AutoGenerated(locked = true, uuid = "a631ea61-bfb6-4292-8423-15ba04389503")
    private String triageStatus;

    /** 分诊时间 */
    @AutoGenerated(locked = true, uuid = "a6fe403e-b4d8-4f3f-8377-d51e22350ed7")
    private Date triageTime;

    /** 就诊应用id */
    @AutoGenerated(locked = true, uuid = "9d13117d-b2ef-4669-ade6-e08b80f539a6")
    private String visitAppId;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "2cf05f2e-3e03-405f-96a6-b8c268e19174")
    private String visitCardId;

    /** 就诊日期 */
    @AutoGenerated(locked = true, uuid = "476edd5a-49a5-4708-b08d-34d15e401eb9")
    private Date visitDate;

    /** 就诊科室ID */
    @AutoGenerated(locked = true, uuid = "ad2213f0-0be4-44b7-a1ca-aabe064de21f")
    private String visitDepartmentId;

    /** 就诊医生ID */
    @AutoGenerated(locked = true, uuid = "a08c3ec3-773a-4dba-9f49-af23e5a2a415")
    private String visitDoctorId;

    /** 门诊就诊流水号 */
    @AutoGenerated(locked = true, uuid = "907649f1-94ae-4c4d-8578-3d024c47d0ab")
    private String visitNumber;

    /** 门诊就诊状态 */
    @AutoGenerated(locked = true, uuid = "153750a6-6553-4a10-a85a-a68bad6c039c")
    private VisitStatusEnum visitStatus;

    /** 就诊类型 */
    @AutoGenerated(locked = true, uuid = "d2b437f4-131f-4766-93ba-decebccc24de")
    private String visitType;

    @AutoGenerated(locked = true)
    public void setCostCategories(String costCategories) {
        this.__$validPropertySet.add("costCategories");
        this.costCategories = costCategories;
    }

    @AutoGenerated(locked = true)
    public void setCostNature(String costNature) {
        this.__$validPropertySet.add("costNature");
        this.costNature = costNature;
    }

    @AutoGenerated(locked = true)
    public void setFirstVisitFlag(Boolean firstVisitFlag) {
        this.__$validPropertySet.add("firstVisitFlag");
        this.firstVisitFlag = firstVisitFlag;
    }

    @AutoGenerated(locked = true)
    public void setGcpProjectId(String gcpProjectId) {
        this.__$validPropertySet.add("gcpProjectId");
        this.gcpProjectId = gcpProjectId;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setIdentityType(String identityType) {
        this.__$validPropertySet.add("identityType");
        this.identityType = identityType;
    }

    @AutoGenerated(locked = true)
    public void setOutpRegisterId(String outpRegisterId) {
        this.__$validPropertySet.add("outpRegisterId");
        this.outpRegisterId = outpRegisterId;
    }

    @AutoGenerated(locked = true)
    public void setPatientDestination(String patientDestination) {
        this.__$validPropertySet.add("patientDestination");
        this.patientDestination = patientDestination;
    }

    @AutoGenerated(locked = true)
    public void setPatientId(String patientId) {
        this.__$validPropertySet.add("patientId");
        this.patientId = patientId;
    }

    @AutoGenerated(locked = true)
    public void setPatientName(String patientName) {
        this.__$validPropertySet.add("patientName");
        this.patientName = patientName;
    }

    @AutoGenerated(locked = true)
    public void setTriageSerialNumber(Long triageSerialNumber) {
        this.__$validPropertySet.add("triageSerialNumber");
        this.triageSerialNumber = triageSerialNumber;
    }

    @AutoGenerated(locked = true)
    public void setTriageStatus(String triageStatus) {
        this.__$validPropertySet.add("triageStatus");
        this.triageStatus = triageStatus;
    }

    @AutoGenerated(locked = true)
    public void setTriageTime(Date triageTime) {
        this.__$validPropertySet.add("triageTime");
        this.triageTime = triageTime;
    }

    @AutoGenerated(locked = true)
    public void setVisitAppId(String visitAppId) {
        this.__$validPropertySet.add("visitAppId");
        this.visitAppId = visitAppId;
    }

    @AutoGenerated(locked = true)
    public void setVisitCardId(String visitCardId) {
        this.__$validPropertySet.add("visitCardId");
        this.visitCardId = visitCardId;
    }

    @AutoGenerated(locked = true)
    public void setVisitDate(Date visitDate) {
        this.__$validPropertySet.add("visitDate");
        this.visitDate = visitDate;
    }

    @AutoGenerated(locked = true)
    public void setVisitDepartmentId(String visitDepartmentId) {
        this.__$validPropertySet.add("visitDepartmentId");
        this.visitDepartmentId = visitDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setVisitDoctorId(String visitDoctorId) {
        this.__$validPropertySet.add("visitDoctorId");
        this.visitDoctorId = visitDoctorId;
    }

    @AutoGenerated(locked = true)
    public void setVisitNumber(String visitNumber) {
        this.__$validPropertySet.add("visitNumber");
        this.visitNumber = visitNumber;
    }

    @AutoGenerated(locked = true)
    public void setVisitStatus(VisitStatusEnum visitStatus) {
        this.__$validPropertySet.add("visitStatus");
        this.visitStatus = visitStatus;
    }

    @AutoGenerated(locked = true)
    public void setVisitType(String visitType) {
        this.__$validPropertySet.add("visitType");
        this.visitType = visitType;
    }
}
