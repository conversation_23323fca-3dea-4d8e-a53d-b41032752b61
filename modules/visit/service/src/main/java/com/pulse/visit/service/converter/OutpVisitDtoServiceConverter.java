package com.pulse.visit.service.converter;

import com.pulse.visit.manager.dto.OutpVisitDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "228fa7c3-9ff7-34b8-be1a-07785682ca5d")
public class OutpVisitDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<OutpVisitDto> OutpVisitDtoConverter(List<OutpVisitDto> outpVisitDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return outpVisitDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
