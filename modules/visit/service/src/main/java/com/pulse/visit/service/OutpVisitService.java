package com.pulse.visit.service;

import com.pulse.appointment_schedule.persist.eo.RegisterFeeEo;
import com.pulse.organization.common.enums.AssetStatusEnum;
import com.pulse.organization.common.enums.AssetTypeEnum;
import com.pulse.organization.manager.dto.*;
import com.pulse.organization.persist.qto.ListTeamWithMemberByStaffIdQto;
import com.pulse.organization.persist.qto.SearchAssetQto;
import com.pulse.organization.persist.qto.SearchRegisterDoctorQto;
import com.pulse.organization.persist.qto.SearchSubOrganizationDepartmentQto;
import com.pulse.visit.manager.dto.OutpVisitBaseDto;
import com.pulse.visit.manager.facade.appointment_schedule.ClinicRegisterServiceInVisitRpcAdapter;
import com.pulse.visit.manager.facade.organization.*;
import com.pulse.visit.service.bto.CreateOutpVisitBto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;
import javax.validation.Valid;

/** Auto generated Service */
@Controller
@Validated
@AutoGenerated(locked = false, uuid = "885321b8-9cb8-3f3e-b000-9437280e5178")
public class OutpVisitService {

    @Resource private OutpVisitBaseDtoService outpVisitBaseDtoService;

    @Resource private OutpVisitBOService outpVisitBOService;

    @Resource
    private OrganizationDepartmentDtoQueryServiceInVisitRpcAdapter
            organizationDepartmentDtoQueryServiceInVisitRpcAdapter;

    @Resource
    private StaffWithExtensionDtoQueryServiceInVisitRpcAdapter
            staffWithExtensionDtoQueryServiceininVisitRpcAdapter;

    @Resource
    private AssetDetailDtoQueryServiceInVisitRpcAdapter assetDetailDtoQueryServiceInVisitRpcAdapter;

    @Resource private StaffDetailDtoServiceInVisitRpcAdapter staffDetailDtoServiceInVisitRpcAdapter;

    @Resource private ClinicRegisterServiceInVisitRpcAdapter clinicRegisterServiceInVisitRpcAdapter;

    @Resource
    private TeamWithMemberDtoQueryServiceInVisitRpcAdapter
            teamWithMemberDtoQueryServiceInVisitRpcAdapter;

    @PublicInterface(
            id = "0adbe350-50d0-4a48-b22e-ebd724ca9e0f",
            module = "visit",
            moduleId = "20a9027a-a071-49bb-93db-29c8446b432c",
            version = "1748327476196",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "0adbe350-50d0-4a48-b22e-ebd724ca9e0f")
    public List<String> getReceptionDepartment(String doctorId, String campusId, String IP) {
        // TODO implement method
        validateInput(doctorId, campusId, IP);

        // 查询医生信息
        StaffWithExtensionDto staffWithExtensionDto = getFirstDoctor(doctorId);

        // 查询科室列表并筛选出符合院区的科室ID
        List<String> departmentIds = getDepartmentIdsByCampus(staffWithExtensionDto, campusId);

        // 查询IP资产相关的组织ID
        List<String> assetOrgIds = getAssetOrganizationIds(IP);

        //  查询团队成员
        List<String> teamWithIds = getTeamWithMembers(doctorId);

        // 合并结果
        Stream<String> stream1 =
                Optional.ofNullable(departmentIds).orElse(Collections.emptyList()).stream();
        Stream<String> stream2 =
                Optional.ofNullable(teamWithIds).orElse(Collections.emptyList()).stream();
        Stream<String> stream3 =
                Optional.ofNullable(assetOrgIds).orElse(Collections.emptyList()).stream();

        return Stream.of(stream1, stream2, stream3)
                .flatMap(s -> s)
                .distinct()
                .collect(Collectors.toList());
    }

    private List<String> getTeamWithMembers(String doctorId) {

        ListTeamWithMemberByStaffIdQto staffIdQto = new ListTeamWithMemberByStaffIdQto();
        staffIdQto.setStaffIdIs(doctorId);
        List<TeamWithMemberDto> teamMemberWithStaffDtoList =
                teamWithMemberDtoQueryServiceInVisitRpcAdapter.listTeamWithMemberByStaffId(
                        staffIdQto);

        if (teamMemberWithStaffDtoList == null || teamMemberWithStaffDtoList.isEmpty()) {
            return List.of();
        }

        return teamMemberWithStaffDtoList.stream()
                .map(TeamWithMemberDto::getLeadOrganizationId) // 获取 leadOrganizationId
                .filter(Objects::nonNull) // 过滤 null
                .distinct() // 去重（如有需要）
                .collect(Collectors.toList());
    }

    // 查询医生并取第一条
    private StaffWithExtensionDto getFirstDoctor(String doctorId) {
        SearchRegisterDoctorQto qtoDoctor = new SearchRegisterDoctorQto();
        qtoDoctor.setStaffNumberIs(doctorId);
        qtoDoctor.setRegisterDoctorFlagIs(true);
        qtoDoctor.setRegisterDoctorEnableFlagIs(true);
        qtoDoctor.setSize(100);
        qtoDoctor.setFrom(0);

        List<StaffWithExtensionDto> staffWithExtensionDtos =
                staffWithExtensionDtoQueryServiceininVisitRpcAdapter.searchRegisterDoctor(
                        qtoDoctor);

        if (staffWithExtensionDtos == null || staffWithExtensionDtos.isEmpty()) {
            return null;
        }

        return staffWithExtensionDtos.get(0);
    }

    // 查询科室并过滤出指定院区的科室ID
    private List<String> getDepartmentIdsByCampus(
            StaffWithExtensionDto staffWithExtensionDto, String campusId) {
        if (staffWithExtensionDto == null) {
            return null;
        }
        SearchSubOrganizationDepartmentQto departmentQto = new SearchSubOrganizationDepartmentQto();
        departmentQto.setDepartmentParentDepartmentOrganizationIdIs(
                staffWithExtensionDto.getOrganization().getParentId());

        List<OrganizationDepartmentDto> departmentDtos =
                organizationDepartmentDtoQueryServiceInVisitRpcAdapter
                        .searchSubOrganizationDepartment(departmentQto);

        if (departmentDtos == null || departmentDtos.isEmpty()) {
            return null;
        }

        return departmentDtos.stream()
                .filter(dto -> campusId.equals(dto.getDepartment().getCampusOrganizationId()))
                .map(OrganizationDepartmentDto::getId)
                .collect(Collectors.toList());
    }

    // 查询IP资产相关组织ID
    private List<String> getAssetOrganizationIds(String IP) {
        SearchAssetQto qtoAsset = new SearchAssetQto();
        qtoAsset.setAssetTypeIs(AssetTypeEnum.COMPUTER);
        qtoAsset.setStatusIs(AssetStatusEnum.IN_USE);
        qtoAsset.setNameLike(IP);
        qtoAsset.setSize(100);
        qtoAsset.setFrom(0);

        List<AssetDetailDto> assetDetails =
                assetDetailDtoQueryServiceInVisitRpcAdapter.searchAsset(qtoAsset);

        if (assetDetails == null || assetDetails.isEmpty()) {
            return List.of();
        }

        return assetDetails.stream()
                .flatMap(
                        asset ->
                                Optional.ofNullable(asset.getAssetOrganizationList()).stream()
                                        .flatMap(List::stream))
                .map(org -> org.getOrganization().getId())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    // 输入参数校验
    private void validateInput(String doctorId, String campusId, String IP) {
        if (doctorId == null || doctorId.isEmpty()) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "医生ID不能为空");
        }
        if (campusId == null || campusId.isEmpty()) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "院区ID不能为空");
        }
        if (IP == null || IP.isEmpty()) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "IP不能为空");
        }
    }

    /** 保存门诊就诊信息，用于保存患者的就诊记录 */
    @PublicInterface(
            id = "64972b3e-9cb1-4f7f-b796-2b9702e4fd1b",
            module = "visit",
            moduleId = "20a9027a-a071-49bb-93db-29c8446b432c",
            version = "1746597690153",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "64972b3e-9cb1-4f7f-b796-2b9702e4fd1b")
    public OutpVisitBaseDto saveOutpVisit(@Valid CreateOutpVisitBto createOutpVisitBto) {
        // 调用BOService保存就诊信息
        String visitId = outpVisitBOService.createOutpVisit(createOutpVisitBto);

        // 根据保存后的ID获取就诊信息并返回
        return outpVisitBaseDtoService.getById(visitId);
    }

    @PublicInterface(
            id = "a40f52e2-e4df-4fff-9bd0-c75dd40608fb",
            module = "visit",
            moduleId = "20a9027a-a071-49bb-93db-29c8446b432c",
            version = "1748490194340",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "a40f52e2-e4df-4fff-9bd0-c75dd40608fb")
    public List<RegisterFeeEo> getRegisterFee(String doctorId) {
        // TODO implement method
        StaffDetailDto staffDetailDto = staffDetailDtoServiceInVisitRpcAdapter.getById(doctorId);
        if (staffDetailDto == null) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "员工不存在");
        }
        List<RegisterFeeEo> registerFeeEos =
                clinicRegisterServiceInVisitRpcAdapter.countRegisterFee(
                        staffDetailDto.getRegisterTypeList(), null);
        return registerFeeEos;
    }
}
