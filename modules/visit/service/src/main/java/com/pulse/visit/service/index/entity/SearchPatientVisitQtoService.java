package com.pulse.visit.service.index.entity;

import com.pulse.visit.persist.mapper.SearchPatientVisitQtoDao;
import com.pulse.visit.persist.qto.SearchPatientVisitQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "67e1f2b5-143b-4042-b4cd-a2aab9d36e58|QTO|SERVICE")
public class SearchPatientVisitQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchPatientVisitQtoDao searchPatientVisitMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "67e1f2b5-143b-4042-b4cd-a2aab9d36e58-query")
    public List<String> query(SearchPatientVisitQto qto) {
        return searchPatientVisitMapper.query(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchPatientVisitQto qto) {
        return searchPatientVisitMapper.count(qto);
    }
}
