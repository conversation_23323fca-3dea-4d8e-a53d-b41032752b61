package com.pulse.visit.service.bto;

import com.pulse.visit.common.enums.VisitStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

import javax.validation.Valid;

/**
 * <b>[源自]</b> OutpVisit
 *
 * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
 */
@Getter
@NoArgsConstructor
@AutoGenerated(locked = true, uuid = "405bf84c-4dc7-44a5-ad49-981f8b73c86e|BTO|DEFINITION")
public class CreateOutpVisitEncounterBto {
    /** 系统追踪属性是否设置，业务勿用！ */
    @AutoGenerated(locked = true)
    private HashSet __$validPropertySet = new HashSet<String>();

    /** 费用分类 */
    @AutoGenerated(locked = true, uuid = "86b375c2-b327-48f6-b716-efeb57bb4464")
    private String costCategories;

    /** 费用性质 */
    @AutoGenerated(locked = true, uuid = "a4c1d692-c84d-46ad-b670-d828200a94fa")
    private String costNature;

    /** 初诊标识 */
    @AutoGenerated(locked = true, uuid = "2198225d-b54f-44ab-93c0-2c8e11e30df3")
    private Boolean firstVisitFlag;

    /** gcp项目ID */
    @AutoGenerated(locked = true, uuid = "200090ac-616f-4e43-b0c1-9236bece7575")
    private String gcpProjectId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "9c8749ae-4203-4cbc-9af2-5cbc73102ffa")
    private String id;

    /** 患者身份 */
    @AutoGenerated(locked = true, uuid = "c91b996e-25d5-4aba-a839-db0f498fc377")
    private String identityType;

    /** 挂号ID */
    @AutoGenerated(locked = true, uuid = "b6bd1fff-365b-43de-9e0a-48c3180ec1da")
    private String outpRegisterId;

    @Valid
    @AutoGenerated(locked = true, uuid = "7030beda-3147-41cb-8945-28fb40ddef24")
    private List<CreateOutpVisitEncounterBto.OutpVisitEncounterBto> outpVisitEncounterBtoList;

    /** 病人去向 */
    @AutoGenerated(locked = true, uuid = "062852b2-8666-4872-81fd-c2a38ae65997")
    private String patientDestination;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "feff7b1a-4981-4328-a771-902d29b4251f")
    private String patientId;

    /** 患者姓名 */
    @AutoGenerated(locked = true, uuid = "30be5151-48e5-4a29-8c92-4d23435dc66b")
    private String patientName;

    /** 分诊序号 */
    @AutoGenerated(locked = true, uuid = "507de12f-132c-4c81-bf15-09f90ec9bf2c")
    private Long triageSerialNumber;

    /** 分诊状态 */
    @AutoGenerated(locked = true, uuid = "e9d13f69-4f8b-4dfb-a60b-e2e3cf6669df")
    private String triageStatus;

    /** 分诊时间 */
    @AutoGenerated(locked = true, uuid = "233c8c46-61d4-496a-befa-7720475fe1f6")
    private Date triageTime;

    /** 就诊应用id */
    @AutoGenerated(locked = true, uuid = "6735ce4d-58e0-4e69-8209-de7b1bf4b810")
    private String visitAppId;

    /** 就诊卡号 */
    @AutoGenerated(locked = true, uuid = "bd8875ca-0d74-4b6f-896b-5264f8366d53")
    private String visitCardId;

    /** 就诊日期 */
    @AutoGenerated(locked = true, uuid = "e3c13c34-e5de-45b6-ade6-81a4b03ee8d6")
    private Date visitDate;

    /** 就诊科室ID */
    @AutoGenerated(locked = true, uuid = "01cdcf6b-7e2c-473d-adc8-88145beb1502")
    private String visitDepartmentId;

    /** 就诊医生ID */
    @AutoGenerated(locked = true, uuid = "272a40cc-c1c0-42f1-b48c-4c0f8a256cf9")
    private String visitDoctorId;

    /** 门诊就诊流水号 */
    @AutoGenerated(locked = true, uuid = "622da704-8e43-4ed5-8cb4-0b4beda4be00")
    private String visitNumber;

    /** 门诊就诊状态 */
    @AutoGenerated(locked = true, uuid = "36ca81de-30a2-4ff0-a274-8b29e0bf94de")
    private VisitStatusEnum visitStatus;

    /** 就诊类型 */
    @AutoGenerated(locked = true, uuid = "a8e3b0ee-154d-4a71-a3f4-47c826e6fe66")
    private String visitType;

    @AutoGenerated(locked = true)
    public void setCostCategories(String costCategories) {
        this.__$validPropertySet.add("costCategories");
        this.costCategories = costCategories;
    }

    @AutoGenerated(locked = true)
    public void setCostNature(String costNature) {
        this.__$validPropertySet.add("costNature");
        this.costNature = costNature;
    }

    @AutoGenerated(locked = true)
    public void setFirstVisitFlag(Boolean firstVisitFlag) {
        this.__$validPropertySet.add("firstVisitFlag");
        this.firstVisitFlag = firstVisitFlag;
    }

    @AutoGenerated(locked = true)
    public void setGcpProjectId(String gcpProjectId) {
        this.__$validPropertySet.add("gcpProjectId");
        this.gcpProjectId = gcpProjectId;
    }

    @AutoGenerated(locked = true)
    public void setId(String id) {
        this.__$validPropertySet.add("id");
        this.id = id;
    }

    @AutoGenerated(locked = true)
    public void setIdentityType(String identityType) {
        this.__$validPropertySet.add("identityType");
        this.identityType = identityType;
    }

    @AutoGenerated(locked = true)
    public void setOutpRegisterId(String outpRegisterId) {
        this.__$validPropertySet.add("outpRegisterId");
        this.outpRegisterId = outpRegisterId;
    }

    @AutoGenerated(locked = true)
    public void setOutpVisitEncounterBtoList(
            List<CreateOutpVisitEncounterBto.OutpVisitEncounterBto> outpVisitEncounterBtoList) {
        this.__$validPropertySet.add("outpVisitEncounterBtoList");
        this.outpVisitEncounterBtoList = outpVisitEncounterBtoList;
    }

    @AutoGenerated(locked = true)
    public void setPatientDestination(String patientDestination) {
        this.__$validPropertySet.add("patientDestination");
        this.patientDestination = patientDestination;
    }

    @AutoGenerated(locked = true)
    public void setPatientId(String patientId) {
        this.__$validPropertySet.add("patientId");
        this.patientId = patientId;
    }

    @AutoGenerated(locked = true)
    public void setPatientName(String patientName) {
        this.__$validPropertySet.add("patientName");
        this.patientName = patientName;
    }

    @AutoGenerated(locked = true)
    public void setTriageSerialNumber(Long triageSerialNumber) {
        this.__$validPropertySet.add("triageSerialNumber");
        this.triageSerialNumber = triageSerialNumber;
    }

    @AutoGenerated(locked = true)
    public void setTriageStatus(String triageStatus) {
        this.__$validPropertySet.add("triageStatus");
        this.triageStatus = triageStatus;
    }

    @AutoGenerated(locked = true)
    public void setTriageTime(Date triageTime) {
        this.__$validPropertySet.add("triageTime");
        this.triageTime = triageTime;
    }

    @AutoGenerated(locked = true)
    public void setVisitAppId(String visitAppId) {
        this.__$validPropertySet.add("visitAppId");
        this.visitAppId = visitAppId;
    }

    @AutoGenerated(locked = true)
    public void setVisitCardId(String visitCardId) {
        this.__$validPropertySet.add("visitCardId");
        this.visitCardId = visitCardId;
    }

    @AutoGenerated(locked = true)
    public void setVisitDate(Date visitDate) {
        this.__$validPropertySet.add("visitDate");
        this.visitDate = visitDate;
    }

    @AutoGenerated(locked = true)
    public void setVisitDepartmentId(String visitDepartmentId) {
        this.__$validPropertySet.add("visitDepartmentId");
        this.visitDepartmentId = visitDepartmentId;
    }

    @AutoGenerated(locked = true)
    public void setVisitDoctorId(String visitDoctorId) {
        this.__$validPropertySet.add("visitDoctorId");
        this.visitDoctorId = visitDoctorId;
    }

    @AutoGenerated(locked = true)
    public void setVisitNumber(String visitNumber) {
        this.__$validPropertySet.add("visitNumber");
        this.visitNumber = visitNumber;
    }

    @AutoGenerated(locked = true)
    public void setVisitStatus(VisitStatusEnum visitStatus) {
        this.__$validPropertySet.add("visitStatus");
        this.visitStatus = visitStatus;
    }

    @AutoGenerated(locked = true)
    public void setVisitType(String visitType) {
        this.__$validPropertySet.add("visitType");
        this.visitType = visitType;
    }

    /**
     * <b>[源自]</b> OutpVisitEncounter
     *
     * <p><b>[操作]</b> CREATE_ON_DUPLICATE_THROW_EX
     */
    @Getter
    @NoArgsConstructor
    public static class OutpVisitEncounterBto {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "f17befdc-99de-42a4-b1b7-ec582a4af952")
        private String id;

        /** 患者id */
        @AutoGenerated(locked = true, uuid = "bef0036c-775a-4829-9cfe-90606834aeed")
        private String patientId;

        /** 接诊记录id */
        @AutoGenerated(locked = true, uuid = "96a65a1f-9c82-4820-84bb-16f5fa41f125")
        private String outpVisitEncounterId;

        /** 接诊次序 */
        @AutoGenerated(locked = true, uuid = "b575d2be-bed1-45b9-9fe7-08d73ef343a5")
        private Long encounterTimes;

        /** 接诊科室ID */
        @AutoGenerated(locked = true, uuid = "e8460b80-7242-4d18-90ff-e148f5c9d637")
        private String visitDepartmentId;

        /** 接诊开始时间 */
        @AutoGenerated(locked = true, uuid = "d108e377-ad83-4d42-a0ab-e03fca7b732b")
        private Date visitStartDate;

        /** 就诊结束时间 */
        @AutoGenerated(locked = true, uuid = "f8508cef-e534-4717-9ff9-f87a6b5d557b")
        private Date visitEndDate;

        /** 接诊医生ID */
        @AutoGenerated(locked = true, uuid = "8d647ed7-c67d-481e-b0c1-9e0399569b51")
        private String encounterDoctorId;

        /** 接诊状态 */
        @AutoGenerated(locked = true, uuid = "4a78d5ce-6386-4415-8e3e-dc3e1be1ed60")
        private String clinicEncounterStatus;

        /** 医疗组号 */
        @AutoGenerated(locked = true, uuid = "10dbbdaf-a8f4-4f7a-ad2e-e67b693eb264")
        private String groupCode;

        /** 医生职称 */
        @AutoGenerated(locked = true, uuid = "a2906a32-617d-4461-8133-c8d13a8f947a")
        private String doctorTitle;

        /** 系统追踪属性是否设置，业务勿用！ */
        @AutoGenerated(locked = true)
        private HashSet __$validPropertySet = new HashSet<String>();

        @AutoGenerated(locked = true)
        public void setId(String id) {
            this.__$validPropertySet.add("id");
            this.id = id;
        }

        @AutoGenerated(locked = true)
        public void setPatientId(String patientId) {
            this.__$validPropertySet.add("patientId");
            this.patientId = patientId;
        }

        @AutoGenerated(locked = true)
        public void setOutpVisitEncounterId(String outpVisitEncounterId) {
            this.__$validPropertySet.add("outpVisitEncounterId");
            this.outpVisitEncounterId = outpVisitEncounterId;
        }

        @AutoGenerated(locked = true)
        public void setEncounterTimes(Long encounterTimes) {
            this.__$validPropertySet.add("encounterTimes");
            this.encounterTimes = encounterTimes;
        }

        @AutoGenerated(locked = true)
        public void setVisitDepartmentId(String visitDepartmentId) {
            this.__$validPropertySet.add("visitDepartmentId");
            this.visitDepartmentId = visitDepartmentId;
        }

        @AutoGenerated(locked = true)
        public void setVisitStartDate(Date visitStartDate) {
            this.__$validPropertySet.add("visitStartDate");
            this.visitStartDate = visitStartDate;
        }

        @AutoGenerated(locked = true)
        public void setVisitEndDate(Date visitEndDate) {
            this.__$validPropertySet.add("visitEndDate");
            this.visitEndDate = visitEndDate;
        }

        @AutoGenerated(locked = true)
        public void setEncounterDoctorId(String encounterDoctorId) {
            this.__$validPropertySet.add("encounterDoctorId");
            this.encounterDoctorId = encounterDoctorId;
        }

        @AutoGenerated(locked = true)
        public void setClinicEncounterStatus(String clinicEncounterStatus) {
            this.__$validPropertySet.add("clinicEncounterStatus");
            this.clinicEncounterStatus = clinicEncounterStatus;
        }

        @AutoGenerated(locked = true)
        public void setGroupCode(String groupCode) {
            this.__$validPropertySet.add("groupCode");
            this.groupCode = groupCode;
        }

        @AutoGenerated(locked = true)
        public void setDoctorTitle(String doctorTitle) {
            this.__$validPropertySet.add("doctorTitle");
            this.doctorTitle = doctorTitle;
        }
    }
}
