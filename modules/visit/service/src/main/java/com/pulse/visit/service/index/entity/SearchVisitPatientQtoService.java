package com.pulse.visit.service.index.entity;

import com.pulse.visit.persist.mapper.SearchVisitPatientQtoDao;
import com.pulse.visit.persist.qto.SearchVisitPatientQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "67e2ef5c-67d4-4bf5-aa61-bbfb6c2b1fbd|QTO|SERVICE")
public class SearchVisitPatientQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private SearchVisitPatientQtoDao searchVisitPatientMapper;

    /** 不分页查询入口 */
    @AutoGenerated(locked = true, uuid = "67e2ef5c-67d4-4bf5-aa61-bbfb6c2b1fbd-query")
    public List<String> query(SearchVisitPatientQto qto) {
        return searchVisitPatientMapper.query(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(SearchVisitPatientQto qto) {
        return searchVisitPatientMapper.count(qto);
    }
}
