package com.pulse.drug_financial.persist.eo;

import com.pulse.drug_financial.common.enums.PriceTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "155ce8dc-f9ab-3d3a-a58a-12950e00b4a0|EO|DEFINITION")
public class UkPackageSpecificationIdPriceTypeEo {
    @AutoGenerated(locked = true, uuid = "c447570a-9793-3628-b92a-cf37234b18e1")
    private String packageOriginSpecificationId;

    @AutoGenerated(locked = true, uuid = "d00571e8-ca01-3090-941c-63ae4d52880a")
    private BigDecimal packageSpecificationPrice;

    @AutoGenerated(locked = true, uuid = "f206281e-787f-3805-9701-4e401df3d85e")
    private PriceTypeEnum priceType;
}
