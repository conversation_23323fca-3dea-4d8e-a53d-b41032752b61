package com.pulse.dictionary_basic.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.dictionary_basic.persist.eo.AgeEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<AgeEo> */
@Converter
@AutoGenerated(locked = true, uuid = "2e669a51-fc31-3878-bf4e-18bb07ef550d")
public class AgeEoListConverter implements AttributeConverter<List<AgeEo>, String> {

    /** convert List<AgeEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(List<AgeEo> ageEoList) {
        if (ageEoList == null || ageEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(ageEoList);
        }
    }

    /** convert DB column to List<AgeEo> */
    @AutoGenerated(locked = true)
    public List<AgeEo> convertToEntityAttribute(String ageEoListJson) {
        if (StrUtil.isEmpty(ageEoListJson)) {
            return new ArrayList<AgeEo>();
        } else {
            return JsonUtils.readObject(ageEoListJson, new TypeReference<List<AgeEo>>() {});
        }
    }
}
