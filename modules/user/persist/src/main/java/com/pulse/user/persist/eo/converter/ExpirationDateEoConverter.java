package com.pulse.user.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.user.persist.eo.ExpirationDateEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for ExpirationDateEo */
@Converter
@AutoGenerated(locked = true, uuid = "cd71dd96-8d54-372d-bd58-3ba9ef21ae96")
public class ExpirationDateEoConverter implements AttributeConverter<ExpirationDateEo, String> {

    /** convert DB column to ExpirationDateEo */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(ExpirationDateEo expirationDateEo) {
        if (expirationDateEo == null) {
            return new String();
        } else {
            return JsonUtils.toJson(expirationDateEo);
        }
    }

    /** convert DB column to ExpirationDateEo */
    @AutoGenerated(locked = true)
    public ExpirationDateEo convertToEntityAttribute(String expirationDateEoJson) {
        if (StrUtil.isEmpty(expirationDateEoJson)) {
            return null;
        } else {
            return JsonUtils.readObject(
                    expirationDateEoJson, new TypeReference<ExpirationDateEo>() {});
        }
    }
}
