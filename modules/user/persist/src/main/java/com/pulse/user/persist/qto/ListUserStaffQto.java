package com.pulse.user.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "ed9bd40b-0f19-44c5-b359-c4687c02e111|QTO|DEFINITION")
public class ListUserStaffQto {
    @AutoGenerated(locked = true, uuid = "b6915ffd-b138-4c16-a0ac-a0b4625eb3bd")
    private Integer from;

    /** 名字 user_info.names */
    @AutoGenerated(locked = true, uuid = "ddf79d78-4d34-40b2-90ad-f86f2043efc6")
    private String searchLike;

    @AutoGenerated(locked = true, uuid = "42dfaca2-2780-49ed-9819-9b616e08b3e3")
    private Integer size;
}
