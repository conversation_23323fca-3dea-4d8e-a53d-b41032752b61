package com.pulse.user.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/** */
@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "b73c1928-997e-4e36-a472-e2a700af4e37|EO|DEFINITION")
public class ExpirationDateEo {
    /** 开始日期 */
    @AutoGenerated(locked = true, uuid = "4499d0a8-0ff6-474f-affc-629ed000ffed")
    private Date startDate;

    /** 结束日期 */
    @AutoGenerated(locked = true, uuid = "180f1a71-1391-4bdf-908b-e892ae43520a")
    private Date endDate;
}
