package com.pulse.user.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pulse.user.common.enums.SessionStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "user_session", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "3ac3f95a-92c8-4708-b838-5edb338adc51|ENTITY|DEFINITION")
public class UserSession {
    @AutoGenerated(locked = true, uuid = "6634775a-db3b-4c49-b66d-df8868bdffa8")
    @TableField(value = "client_device_type")
    private String clientDeviceType;

    @AutoGenerated(locked = true, uuid = "b6df8e85-2ef5-4d8f-bf2f-c26dc6f095be")
    @TableField(value = "client_ip")
    private String clientIp;

    @AutoGenerated(locked = true, uuid = "e0ae4ddf-4828-4af5-b3e5-ca5cf539d6a5")
    @TableField(value = "client_user_proxy_info")
    private String clientUserProxyInfo;

    @AutoGenerated(locked = true, uuid = "29bf27d5-5c7d-4d56-b331-c12e20b6333a")
    @TableField(value = "create_date")
    private Date createDate;

    @AutoGenerated(locked = true, uuid = "89e2d332-23de-4856-8c80-69146b846610")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "93ad679f-d5e8-4ae4-899f-a665fb0eeabd")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    @AutoGenerated(locked = true, uuid = "0386cdcd-3a82-40a8-b293-6bf439f71b47")
    @TableField(value = "expire_time")
    private Date expireTime;

    @AutoGenerated(locked = true, uuid = "1e161810-51a6-437c-914e-e0fbd38dd133")
    @TableId(value = "id")
    private String id;

    @AutoGenerated(locked = true, uuid = "6edb1d97-cbc5-4069-a49c-fef2446b6720")
    @TableField(value = "last_access_time")
    private Date lastAccessTime;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "c3df2d9a-a36e-48a6-9d16-e1abd8ec3d54")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "4320050f-0092-4b43-9067-ce5f221daa25")
    @TableField(value = "name")
    private String name;

    @AutoGenerated(locked = true, uuid = "2eee0daa-a63a-4a2f-960d-9196faf796b8")
    @TableField(value = "session_id")
    private String sessionId;

    @AutoGenerated(locked = true, uuid = "54e17fc5-a1a9-48ed-8c78-4277a7204989")
    @TableField(value = "status")
    private SessionStatusEnum status;

    @AutoGenerated(locked = true, uuid = "048181a1-e8bc-428a-8de2-415d4eaab0a0")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "ddacfa20-32e5-4f03-95c1-ee574f96218a")
    @TableField(value = "user_id")
    private String userId;

    @AutoGenerated(locked = true, uuid = "57a1b4eb-edd4-43ab-acb4-365e362ef690")
    @TableField(value = "user_last_action_time")
    private Date userLastActionTime;
}
