package com.pulse.user.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.pulse.dictionary_business.persist.eo.AddressEo;
import com.pulse.organization.common.enums.GenderEnum;
import com.pulse.user.common.enums.UserStatusEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@TableName(value = "user_info", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "fd2cfc80-842d-4346-b03e-75ac27700bef|ENTITY|DEFINITION")
public class UserInfo {
    @Valid
    @AutoGenerated(locked = true, uuid = "31f8fc1d-f560-4ae0-852a-34c824b8c48d")
    @TableField(value = "address", typeHandler = JacksonTypeHandler.class)
    private AddressEo address;

    @AutoGenerated(locked = true, uuid = "250e9776-c0ee-4485-ad99-1a241c96904d")
    @TableField(value = "age")
    private Integer age;

    @AutoGenerated(locked = true, uuid = "dac3b680-9d34-4d75-90d0-e0879e8cf398")
    @TableField(value = "birth_day")
    private Date birthDay;

    @AutoGenerated(locked = true, uuid = "fe5face7-44f8-47f1-a100-ebe5c6451739")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "6d9462b8-11d8-4a6b-bb23-ac79de5583e3")
    @TableField(value = "deleted_at")
    private Long deletedAt;

    @AutoGenerated(locked = true, uuid = "6b115d53-d8dd-4048-93d4-7fe1be0e8c65")
    @TableField(value = "gender")
    private GenderEnum gender;

    @AutoGenerated(locked = true, uuid = "39d6b461-adaf-4cff-b461-af9d27e7b5cb")
    @TableId(value = "id")
    private String id;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "6a8be5a6-285c-49b4-a1e1-e3bab7432da9")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "eba75c41-6af5-49e6-a816-74627aedd8f7")
    @TableField(value = "names")
    private String names;

    @AutoGenerated(locked = true, uuid = "a3bc6e2b-717c-4aff-b5b6-4744c606bf93")
    @TableField(value = "phone_number")
    private String phoneNumber;

    @AutoGenerated(locked = true, uuid = "f81e3610-9cb7-4ad5-aacd-efc1408363c4")
    @TableField(value = "status")
    private UserStatusEnum status;

    @AutoGenerated(locked = true, uuid = "1b155fe2-0040-4c9f-ada0-b30589addbd2")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "4d04bcf7-7128-4c4f-afa8-bed359896320")
    @TableField(value = "user_expiration_end")
    private Date userExpirationEnd;

    @AutoGenerated(locked = true, uuid = "b960d44d-db56-4e4d-a2e8-36eda2dbcc4c")
    @TableField(value = "user_expiration_start")
    private Date userExpirationStart;

    @AutoGenerated(locked = true, uuid = "1adc4f61-18ca-4f8f-9f1d-7ff6ebe4ccd1")
    @TableField(value = "user_name")
    private String userName;
}
