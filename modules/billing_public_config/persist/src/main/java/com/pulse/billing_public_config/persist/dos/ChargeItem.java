package com.pulse.billing_public_config.persist.dos;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.pulse.billing_public_config.common.enums.ChargeItemSourceEnum;
import com.pulse.billing_public_config.common.enums.ChargePackageTypeEnum;
import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@TableName(value = "charge_item", autoResultMap = true)
@AutoGenerated(locked = true, uuid = "038933f5-63fe-4653-a89e-9c507122be53|ENTITY|DEFINITION")
public class ChargeItem {
    @AutoGenerated(locked = true, uuid = "0c2dea03-d4cd-459f-a953-823854e3e000")
    @TableField(value = "accounting_subject_category")
    private String accountingSubjectCategory;

    @AutoGenerated(locked = true, uuid = "88b6ed7e-b7b3-443e-a677-d8acbfb23da5")
    @TableField(value = "bidding_code")
    private String biddingCode;

    @Valid
    @AutoGenerated(locked = true, uuid = "6263ba1c-229e-4a71-a347-6cc764b899ef")
    @TableField(value = "campus_id_list", typeHandler = JacksonTypeHandler.class)
    private List<String> campusIdList;

    @AutoGenerated(locked = true, uuid = "97b13ee4-2bb6-466c-a072-8ccb99394edc")
    @TableField(value = "charge_item_category")
    private String chargeItemCategory;

    @AutoGenerated(locked = true, uuid = "3862e4ae-9759-54b8-8353-7876e2151f47")
    @TableField(value = "created_at")
    private Date createdAt;

    @AutoGenerated(locked = true, uuid = "6d3e2172-79ed-468c-b1d4-339a1decbc44")
    @TableField(value = "created_by")
    private String createdBy;

    @AutoGenerated(locked = true, uuid = "c08f8b66-89af-40f4-af5e-11b56b964254")
    @TableField(value = "enable_flag")
    private Boolean enableFlag;

    @AutoGenerated(locked = true, uuid = "d279f899-9634-45be-9ea0-a40c535eae35")
    @TableField(value = "extension_field")
    private String extensionField;

    @AutoGenerated(locked = true, uuid = "10c1dc31-4f44-452c-bb2c-8bc818e1c322")
    @TableField(value = "firm_code")
    private String firmCode;

    @AutoGenerated(locked = true, uuid = "7e204ef3-6e29-4648-ac9c-61e8f56b2056")
    @TableField(value = "independent_pricing_flag")
    private Boolean independentPricingFlag;

    @Valid
    @AutoGenerated(locked = true, uuid = "942f1430-fe1d-410d-90c3-66a1fffba700")
    @TableField(value = "input_code", typeHandler = JacksonTypeHandler.class)
    private InputCodeEo inputCode;

    @AutoGenerated(locked = true, uuid = "5ae47b21-4e13-475c-bdac-6c218eeabfa4")
    @TableField(value = "insurance_item_level")
    private String insuranceItemLevel;

    @AutoGenerated(locked = true, uuid = "69f07745-2ed2-4f3e-983c-dc6c86fb6646")
    @TableField(value = "insurance_nation_code")
    private String insuranceNationCode;

    @AutoGenerated(locked = true, uuid = "4a7bb541-19f0-452f-bfb2-5ec38c26af54")
    @TableField(value = "insurance_nation_name")
    private String insuranceNationName;

    @AutoGenerated(locked = true, uuid = "421dc3ec-0376-4f95-ab29-ecbf519bb733")
    @TableField(value = "insurance_province_code")
    private String insuranceProvinceCode;

    @AutoGenerated(locked = true, uuid = "b85e4038-5cf3-4a56-9eb7-e064c53f0fe4")
    @TableField(value = "item_category")
    private String itemCategory;

    @AutoGenerated(locked = true, uuid = "95762ca2-486a-468d-8e87-1cd8c710a15c")
    @TableId(value = "item_code")
    private String itemCode;

    @AutoGenerated(locked = true, uuid = "0b52b3ba-9f64-4a7d-930f-cff0615d50c5")
    @TableField(value = "item_connotation")
    private String itemConnotation;

    @AutoGenerated(locked = true, uuid = "d292c4d6-e081-4d29-8d68-d4e45d19598d")
    @TableField(value = "item_name")
    private String itemName;

    @AutoGenerated(locked = true, uuid = "7d167d03-6ad7-457a-9c6b-99a045b0e99a")
    @TableField(value = "item_specification")
    private String itemSpecification;

    @AutoGenerated(locked = true, uuid = "2f18b942-6653-45e3-802a-6c45baf02f35")
    @TableField(value = "leaf_flag")
    private Boolean leafFlag;

    /** 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "f60ba973-96b2-4742-9795-85c9838d905f")
    @TableField(value = "lock_version")
    private Long lockVersion;

    @AutoGenerated(locked = true, uuid = "202d4bc6-83f0-46ec-b13f-162655686b80")
    @TableField(value = "medical_record_category")
    private String medicalRecordCategory;

    @AutoGenerated(locked = true, uuid = "808b58aa-ef05-4437-ac02-17b0dedcc7a7")
    @TableField(value = "medical_record_sub_category")
    private String medicalRecordSubCategory;

    @AutoGenerated(locked = true, uuid = "230e087c-a7ef-465b-ad0f-a52e0cbd6e70")
    @TableField(value = "organization_id")
    private String organizationId;

    @AutoGenerated(locked = true, uuid = "4b255d81-5c62-440d-b819-580bb2cebd86")
    @TableField(value = "package_type")
    private ChargePackageTypeEnum packageType;

    @AutoGenerated(locked = true, uuid = "62a0ed3d-46fb-46a2-84b3-d5841d00b436")
    @TableField(value = "parent_item_code")
    private String parentItemCode;

    @AutoGenerated(locked = true, uuid = "13261b3c-5dec-46eb-9880-4722fd3a6bb1")
    @TableField(value = "reckoning_category")
    private String reckoningCategory;

    @AutoGenerated(locked = true, uuid = "a2cae555-2d8e-418e-9e22-08eea36db027")
    @TableField(value = "remark")
    private String remark;

    @AutoGenerated(locked = true, uuid = "2cb5443b-cf74-4e38-b525-69796ba706f7")
    @TableField(value = "single_settle_flag")
    private Boolean singleSettleFlag;

    @AutoGenerated(locked = true, uuid = "18621de8-121a-4019-ae64-6ba7b7da80dc")
    @TableField(value = "sort_number")
    private Long sortNumber;

    @AutoGenerated(locked = true, uuid = "17b9e1c2-738c-43e9-9cd9-95ebe8903d8e")
    @TableField(value = "source")
    private ChargeItemSourceEnum source;

    @AutoGenerated(locked = true, uuid = "1ab503d4-4cde-48e9-8662-71bfc8da563e")
    @TableField(value = "standard_code")
    private String standardCode;

    @AutoGenerated(locked = true, uuid = "18977f86-8c87-4edd-bcfd-b82746d5861f")
    @TableField(value = "synchronized_flag")
    private Boolean synchronizedFlag;

    @AutoGenerated(locked = true, uuid = "68712ef8-fcfe-45b8-82e4-35dfd1715927")
    @TableField(value = "tcm_medical_record_category")
    private String tcmMedicalRecordCategory;

    @AutoGenerated(locked = true, uuid = "a2fe4445-4f7f-4f5d-b667-996010911bab")
    @TableField(value = "unit")
    private String unit;

    @AutoGenerated(locked = true, uuid = "eec9b386-3a78-5c13-a634-2146a6373d8d")
    @TableField(value = "updated_at")
    private Date updatedAt;

    @AutoGenerated(locked = true, uuid = "bd17f905-3044-4678-900c-60c0c9e40d9a")
    @TableField(value = "updated_by")
    private String updatedBy;

    @Valid
    @AutoGenerated(locked = true, uuid = "fdbcd98b-e2d6-4aad-9c9f-9c1fc34e5a4a")
    @TableField(value = "use_scope_list", typeHandler = JacksonTypeHandler.class)
    private List<String> useScopeList;
}
