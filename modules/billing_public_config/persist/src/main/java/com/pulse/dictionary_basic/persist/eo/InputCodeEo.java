package com.pulse.dictionary_basic.persist.eo;

import com.vs.code.AutoGenerated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** */
@Getter
@Setter
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@AutoGenerated(locked = false, uuid = "bf97657a-3cdb-399b-b561-f3725860a307|EO|DEFINITION")
public class InputCodeEo {
    /** 拼音码 */
    @AutoGenerated(locked = true, uuid = "6608942e-c230-3d68-a294-b5e2e3014aac")
    private String pinyin;

    /** 五笔码 */
    @AutoGenerated(locked = true, uuid = "fd5ba361-ad4f-3449-b0ce-6598176a6684")
    private String wubi;

    /** 自定义 */
    @AutoGenerated(locked = true, uuid = "b8d4698e-1f34-3294-8e6d-9933a7f9689e")
    private String custom;
}
