package com.pulse.consulting_room.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "e407cd90-3602-4ac1-b21e-6b19ef6706dd|QTO|DEFINITION")
public class SearchConsultingRoomQto {
    /** 诊室区域 consulting_room.area */
    @AutoGenerated(locked = true, uuid = "a5eb7775-ab9b-49ae-b73a-562e3f22dfc8")
    private String areaIdIs;

    /** 主键 organization.id */
    @AutoGenerated(locked = true, uuid = "439f088f-c0dd-47fc-92aa-4113b5943fa3")
    private String campusOrganizationIdIs;

    /** 诊室门牌号 consulting_room.clinic_room_number */
    @AutoGenerated(locked = true, uuid = "58fb8720-1e68-4636-8490-4b2d87ae5dfb")
    private String clinicRoomNumberIs;

    /** 诊室名称(患者) consulting_room.display_name */
    @AutoGenerated(locked = true, uuid = "b066a2af-c1df-4746-aa3d-db37dc315580")
    private String displayNameLike;

    @AutoGenerated(locked = true, uuid = "bfd89b01-f743-44e8-b5c7-6c4423e66b28")
    private Integer from;

    /** 作废标志 consulting_room.invalid_flag */
    @AutoGenerated(locked = true, uuid = "1090289d-6331-4dc6-8dc9-bd5bbacdfa74")
    private Boolean invalidFlagIs;

    /** 诊室名称（排班） consulting_room.name */
    @AutoGenerated(locked = true, uuid = "de3cfeaa-fc22-417d-acc2-fb836ed06dd5")
    private String nameLike;

    /** 简码 consulting_room.short_code */
    @AutoGenerated(locked = true, uuid = "5ed297d7-65ad-4176-85b8-74d38f6435b0")
    private String shortCodeLike;

    @AutoGenerated(locked = true, uuid = "50508d39-1ea0-477e-b7e7-f49dab7966d2")
    private Integer size;
}
