package com.pulse.permission.persist.eo.converter;

import cn.hutool.core.util.StrUtil;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.permission.persist.eo.IdxEnableExpiredEo;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/** converter for List<IdxEnableExpiredEo> */
@Converter
@AutoGenerated(locked = true, uuid = "5bd2f537-6e1b-30c3-a0f6-021351086d1f")
public class IdxEnableExpiredEoListConverter
        implements AttributeConverter<List<IdxEnableExpiredEo>, String> {

    /** convert List<IdxEnableExpiredEo> to DB Column. */
    @AutoGenerated(locked = true)
    public String convertToDatabaseColumn(List<IdxEnableExpiredEo> idxEnableExpiredEoList) {
        if (idxEnableExpiredEoList == null || idxEnableExpiredEoList.size() == 0) {
            return new String();
        } else {
            return JsonUtils.toJson(idxEnableExpiredEoList);
        }
    }

    /** convert DB column to List<IdxEnableExpiredEo> */
    @AutoGenerated(locked = true)
    public List<IdxEnableExpiredEo> convertToEntityAttribute(String idxEnableExpiredEoListJson) {
        if (StrUtil.isEmpty(idxEnableExpiredEoListJson)) {
            return new ArrayList<IdxEnableExpiredEo>();
        } else {
            return JsonUtils.readObject(
                    idxEnableExpiredEoListJson, new TypeReference<List<IdxEnableExpiredEo>>() {});
        }
    }
}
